<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RoomDialog</class>
 <widget class="QDialog" name="RoomDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="wdg_title" native="true">
     <property name="minimumSize">
      <size>
       <width>780</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>40</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="lb_title">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>房间号：111111111</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>469</width>
          <height>15</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="pb_min">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="text">
         <string>一</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pb_max">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>幼圆</family>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="text">
         <string>口</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pb_close">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="text">
         <string>X</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>3</number>
     </property>
     <item>
      <widget class="QWidget" name="wdg_left" native="true">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="UserShow" name="wdg_userShow" native="true"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="wdg_right" native="true">
       <property name="minimumSize">
        <size>
         <width>251</width>
         <height>518</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>251</width>
         <height>100000</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QScrollArea" name="scrollArea">
          <property name="frameShape">
           <enum>QFrame::Shape::NoFrame</enum>
          </property>
          <property name="horizontalScrollBarPolicy">
           <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
          </property>
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>251</width>
             <height>518</height>
            </rect>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>10</number>
            </property>
            <property name="rightMargin">
             <number>10</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QWidget" name="wdg_list" native="true"/>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Orientation::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>495</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="wdg_bottom" native="true">
     <property name="minimumSize">
      <size>
       <width>780</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>40</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="topMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QCheckBox" name="cb_video">
        <property name="text">
         <string>视频</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="cb_audio">
        <property name="text">
         <string>音频</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="cb_desk">
        <property name="text">
         <string>桌面</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="cb_moji">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <item>
         <property name="text">
          <string>无滤镜</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>兔耳朵</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>圣诞帽子</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>377</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="pb_quit">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="text">
         <string>退出房间</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UserShow</class>
   <extends>QWidget</extends>
   <header>usershow.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
