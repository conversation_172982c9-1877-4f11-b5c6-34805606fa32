#include "roomdialog.h"
#include "ui_roomdialog.h"
#include <QMessageBox>

RoomDialog::RoomDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::RoomDialog)
{
    ui->setupUi(this);
    m_mainLayout = new QVBoxLayout;//初始化

    //设置
    m_mainLayout->setContentsMargins(0,0,0,0);//设置外边距
    m_mainLayout->setSpacing(5);//设置控件彼此之间的距离

    //设置一个垂直布局的画布，可以向这个里面添加控件
    ui->wdg_list->setLayout(m_mainLayout);

}

RoomDialog::~RoomDialog()
{
    delete ui;
}
//设置房间信息
void RoomDialog::slot_setInfo(QString roomid)
{
    QString title = QString("房间号: %1").arg(roomid);
    setWindowTitle(title);

    ui->lb_title->setText(title);
}
//添加房间用户信息
void RoomDialog::slot_addUserShow(UserShow *user)
{
    m_mainLayout->addWidget(user);
    m_mapIDToUserShow[user->m_id] = user;//加入

}
//退出房间 用户处理
void RoomDialog::slot_removeUserShow(UserShow *user)
{
    user->hide();//先隐藏
    m_mainLayout->removeWidget(user);

}
//根据id 去掉用户
void RoomDialog::slot_removeUserShow(int id)
{
    if(m_mapIDToUserShow.count(id)>0)
    {
        UserShow* user = m_mapIDToUserShow[id];
        slot_removeUserShow(user);
    }
}


//清空函数
void RoomDialog::slot_clearUserShow()
{
    //遍历，清空
    for(auto ite = m_mapIDToUserShow.begin();ite!=m_mapIDToUserShow.end();ite++){
        slot_removeUserShow(ite->second);
    }
}

//退出房间
void RoomDialog::on_pb_close_clicked()
{

        this->close();
}

//退出房间
void RoomDialog::on_pb_quit_clicked()
{

        this->close();
}

//关闭事件
//关闭事件
void RoomDialog::closeEvent(QCloseEvent *event)
{
    if( QMessageBox::question(this , "提示" , "是否退出会议？")== QMessageBox::Yes )//点击按钮，询问是否退出
    {
        //点YES
        Q_EMIT SIG_close();//发送信号
        event->accept();//事件执行
        return;
    }
    else
    {
        event->ignore();//事件忽略
    }
}
