#pragma once
#ifndef MD5_H//如果没有定义MD5
#define MD5_H//则定义它

#include <string>
#include <fstream>//引入文件输入输出流库

/* Type define */
typedef unsigned char MD5Byte;//定义MD5vyte类型为无符号字符
typedef unsigned long ulong;//定义ulong类型为无符号长整形

using std::string;//使用std命名空间的string类型
using std::ifstream;//使用std命名空间的ifstream类型

/* MD5 declaration. */
class MD5 {
public:
    MD5();//默认构造函数
    MD5(const void *input, size_t length);//接受输入缓冲区的构造函数
    MD5(const string &str);//接受字符串的构造函数
    MD5(ifstream &in);//接受文件流的构造函数
    void update(const void *input, size_t length);//更新MD5计算状态，接受输入缓冲区
    void update(const string &str);//更新MD5计算状态，接受字节流
    void update(ifstream &in);//更新MD5计算状态，接受文件流
    const MD5Byte* digest();//获取最终的MD5值
    string toString();//将MD5值转换为字符串形式
    void reset();//重置MD5计算状态

private:
    void update(const MD5Byte *input, size_t length);//更新MD5计算状态，接受字节数组
    void final();//MD5计算的最终步骤
    void transform(const MD5Byte block[64]);//对64字节的块进行变换
    void encode(const ulong *input, MD5Byte *output, size_t length);//解码
    void decode(const MD5Byte *input, ulong *output, size_t length);//解码
    string bytesToHexString(const MD5Byte *input, size_t length);//将字节数组转换为十六进制字符串

    /* 禁止拷贝构造和赋值*/
    MD5(const MD5&);//禁止拷贝构造
    MD5& operator=(const MD5&);//禁止赋值
private:
    ulong _state[4];    /* 存储MD5的状态(ABCD) */
    ulong _count[2];    /* 存储处理的数据位数（低位在前） */
    MD5Byte _buffer[64];   /* 输入缓冲区 */
    MD5Byte _digest[16];   /* 存储最终的MD5值 */
    bool _finished;     /* 标记计算是否结束 ? */

    static const MD5Byte PADDING[64];  /* 填充数据，保证输入数据长度符号要求 */
    static const char HEX[16];//十六进制字符表
    static const size_t BUFFER_SIZE = 1024;//缓冲区大小
};

#endif/*如果没有定义MD5_H, 则结束文件保护宏*/
