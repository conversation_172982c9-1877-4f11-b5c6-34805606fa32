#ifndef ROOMDIALOG_H
#define ROOMDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include "usershow.h"
namespace Ui {
class RoomDialog;
}

class RoomDialog : public QDialog
{
    Q_OBJECT

signals:
    void SIg_close();//关闭信号

public:
    explicit RoomDialog(QWidget *parent = nullptr);
    ~RoomDialog();

public slots:
    void slot_setInfo(QString roomid);
    void slot_addUserShow(UserShow* user);//添加房间用户信息
    void slot_removeUserShow(UserShow* user);//退出房间
    void slot_removeUserShow(int id);//重载 退出房间 根据id去掉
    void slot_clearUserShow();//清空函数


private slots:
    void on_pb_close_clicked();

    void on_pb_quit_clicked();

    void closeEvent(QCloseEvent * event);//关闭事件

private:
    Ui::RoomDialog *ui;
    QVBoxLayout* m_mainLayout;//主层
    std::map<int,UserShow*> m_mapIDToUserShow;//id映射到usershow上

};

#endif // ROOMDIALOG_H
