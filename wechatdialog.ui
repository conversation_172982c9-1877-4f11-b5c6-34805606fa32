<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WeChatDialog</class>
 <widget class="QDialog" name="WeChatDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>540</width>
    <height>620</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WeChatDialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="widget" native="true">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>520</width>
     <height>600</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(81, 81, 81);</string>
   </property>
   <widget class="QScrollArea" name="scrollArea">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>180</y>
      <width>520</width>
      <height>420</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>10</pointsize>
     </font>
    </property>
    <property name="frameShape">
     <enum>QFrame::Shape::Box</enum>
    </property>
    <property name="widgetResizable">
     <bool>true</bool>
    </property>
    <widget class="QWidget" name="scrollAreaWidgetContents">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>516</width>
       <height>416</height>
      </rect>
     </property>
    </widget>
   </widget>
   <widget class="QToolButton" name="tb_join">
    <property name="geometry">
     <rect>
      <x>80</x>
      <y>100</y>
      <width>80</width>
      <height>70</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>微软雅黑</family>
      <pointsize>10</pointsize>
     </font>
    </property>
    <property name="text">
     <string>加入会议</string>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/images/color.png</normaloff>:/images/color.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>50</width>
      <height>50</height>
     </size>
    </property>
    <property name="toolButtonStyle">
     <enum>Qt::ToolButtonStyle::ToolButtonTextUnderIcon</enum>
    </property>
    <property name="autoRaise">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QToolButton" name="tb_create">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>100</y>
      <width>80</width>
      <height>70</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>微软雅黑</family>
      <pointsize>10</pointsize>
     </font>
    </property>
    <property name="text">
     <string>创建会议</string>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/images/color.png</normaloff>:/images/color.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>50</width>
      <height>50</height>
     </size>
    </property>
    <property name="toolButtonStyle">
     <enum>Qt::ToolButtonStyle::ToolButtonTextUnderIcon</enum>
    </property>
    <property name="autoRaise">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QToolButton" name="tb_tool3">
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>100</y>
      <width>80</width>
      <height>70</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>微软雅黑</family>
      <pointsize>10</pointsize>
     </font>
    </property>
    <property name="text">
     <string>预留</string>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/images/color.png</normaloff>:/images/color.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>50</width>
      <height>50</height>
     </size>
    </property>
    <property name="toolButtonStyle">
     <enum>Qt::ToolButtonStyle::ToolButtonTextUnderIcon</enum>
    </property>
    <property name="autoRaise">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="pb_icon">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>20</y>
      <width>60</width>
      <height>60</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/tx/13.png</normaloff>:/tx/13.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>60</width>
      <height>60</height>
     </size>
    </property>
    <property name="flat">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="lb_name">
    <property name="geometry">
     <rect>
      <x>150</x>
      <y>30</y>
      <width>200</width>
      <height>40</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>微软雅黑</family>
      <pointsize>12</pointsize>
      <bold>false</bold>
     </font>
    </property>
    <property name="text">
     <string>测试用户</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pb_set">
    <property name="geometry">
     <rect>
      <x>400</x>
      <y>40</y>
      <width>40</width>
      <height>40</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/images/sett.png</normaloff>:/images/sett.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>30</width>
      <height>30</height>
     </size>
    </property>
   </widget>
  </widget>
 </widget>
 <resources>
  <include location="resource.qrc"/>
 </resources>
 <connections/>
</ui>
