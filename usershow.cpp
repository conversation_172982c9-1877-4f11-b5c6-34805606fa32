#include "usershow.h"
#include "ui_usershow.h"
#include <QPainter>

UserShow::UserShow(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::UserShow)
{
    ui->setupUi(this);
}

UserShow::~UserShow()
{
    delete ui;
}
//设置房间内的用户信息
void UserShow::slot_setInfo(int id, QString name)
{
    m_id=id;
    m_userName=name;

    //更新
    QString text = QString("用户名: %1").arg(m_userName);
    ui->lb_name->setText(text);//设置控件
}
//重绘事件
void UserShow::paintEvent(QPaintEvent *event)
{
    //画黑背景
    QPainter painter(this);
    painter.setBrush(Qt::black);//设置黑色画刷
    painter.drawRect(0,0,this->width(),this->height());//参数为起始位置，宽度，高度

    //画视频帧

}
