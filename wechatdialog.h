#ifndef WECHATDIALOG_H
#define WECHATDIALOG_H

#include <QDialog>
#include <QCloseEvent>

QT_BEGIN_NAMESPACE
namespace Ui {
class WeChatDialog;
}
QT_END_NAMESPACE

class WeChatDialog : public QDialog
{
    Q_OBJECT

signals:
    void SIG_CLOSE();//关闭信号
    void SIG_createRoom();//创建房间信号
    void SIG_joinRoom();//加入房间信号

public:
    WeChatDialog(QWidget *parent = nullptr);
    ~WeChatDialog();

    void closeEvent(QCloseEvent* event);//关闭事件
    void setInfo(QString name, int icon = 1);

private slots:
    void on_tb_create_clicked();

    void on_tb_join_clicked();

private:
    Ui::WeChatDialog *ui;
};
#endif // WECHATDIALOG_H
