#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>
#include <QCloseEvent>

namespace Ui {
class LoginDialog;
}

class LoginDialog : public QDialog
{
    Q_OBJECT
signals:
    void SIG_loginCommit(QString tel,QString password);//登录提交按钮信号
    void SIG_registerCommit(QString name,QString tel,QString password);//注册提交按钮信号
    void SIG_close();//关闭信号

public:
    explicit LoginDialog(QWidget *parent = nullptr);
    ~LoginDialog();
    //关闭事件
    void closeEvent(QCloseEvent* event);

private slots:
    void on_pb_commit_clicked();

    void on_pb_clear_register_clicked();

    void on_pb_commit_register_clicked();

    void on_pb_clear_clicked();

private:
    Ui::LoginDialog *ui;
};

#endif // LOGINDIALOG_H
