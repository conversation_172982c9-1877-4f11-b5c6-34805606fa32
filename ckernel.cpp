#include "ckernel.h"
#include "qDebug"
#include "packdef.h"
//配置文件？
#include <QSettings>
#include <QApplication>
//判断文件是否存在
#include <QFileInfo>
#include "md5.h"
#include <QMessageBox>
//输入对话框
#include <QInputDialog>
#include <QRegularExpression> //正则表达式头文件 判断房间号是否合理

#define NetPackMap(a) m_netPackMap[a - _DEF_PACK_BASE]
//设置协议映射关系
void Ckernel::setNetPackMap()
{
    memset(m_netPackMap,0,sizeof(m_netPackMap));//初始化协议关系表为0
    //通过基准值设置不同的处理函数-------------这里是03-客户端搭建 差了点，后面补--------
    //m_netPackMap[_DEF_PACK_LOGIN_RS - _DEF_PACK_BASE]= &Ckernel::slot_dealLoginRs;
    NetPackMap(_DEF_PACK_LOGIN_RS)     =   &Ckernel::slot_dealLoginRs;
    NetPackMap(_DEF_PACK_REGISTER_RS)  =   &Ckernel::slot_dealRegisterRs;
    NetPackMap(_DEF_PACK_CREATEROOM_RS)  =   &Ckernel::slot_dealCreateRoomRs;
    NetPackMap(_DEF_PACK_JOINROOM_RS)  =   &Ckernel::slot_dealJoinCRoomRs;//对于加入房间回复的处理
    NetPackMap(_DEF_PACK_ROOM_MEMBER)  =   &Ckernel::slot_dealRoomMemberRq;//房间成员请求回复处理
    NetPackMap(_DEF_PACK_LEAVEROOM_RQ)  =   &Ckernel::slot_dealLeaveRoomRq;//房间成员请求回复处理



}
//初始化配置
void Ckernel::initConfig()
{
    m_serverIP = _DEF_SERVER_IP;
    //路径设置 exe同级的目录下 -> applicationDirPath config.ini
    QString path = QApplication::applicationDirPath()+"/config.ini";
    //判断是否存在
    QFileInfo info(path);
    QSettings settings(path,QSettings::IniFormat,NULL);//有则打开，无则创建
    if(info.exists()){
        //打开配置文件
        //移动到net的组 读取 ip ->addr -->赋值
        settings.beginGroup("Net");
        QVariant ip =  settings.value("ip");
        QString strIP = ip.toString();
        //结束
        settings.endGroup();
        if(!strIP.isEmpty()){
            m_serverIP = strIP;
        }
    }else{
        settings.beginGroup("Net");
        settings.setValue("ip",m_serverIP);
        settings.endGroup();
    }
    qDebug()<<m_serverIP;
    //加载配置文件 如果有配置文件，ip设置为配置文件中的ip

    //           如果无配置文件，写入默认的ip

}




Ckernel::Ckernel(QObject *parent): QObject{parent},m_name(" "),m_id(0),m_roomid(0)
{
    setNetPackMap();//设置协议映射关系
    initConfig();//初始化配置文件
    m_pWeChatDlg = new WeChatDialog;
    //绑定信号和槽函数
    connect(m_pWeChatDlg,SIGNAL(SIG_CLOSE()),this,SLOT(slot_destroy()));
    connect(m_pWeChatDlg,SIGNAL(SIG_createRoom()),this,SLOT(slot_createRoom()));//创建房间
    connect(m_pWeChatDlg,SIGNAL(SIG_joinRoom()),this,SLOT(slot_joinRoom()));//加入房间



    //m_pWeChatDlg->show();
    //创建登录界面
    m_pLoginDlg = new LoginDialog;
    connect(m_pLoginDlg,SIGNAL(SIG_loginCommit(QString,QString)),this,SLOT(slot_loginCommit(QString,QString)));//登录提交
    connect(m_pLoginDlg,SIGNAL(SIG_close()),this,SLOT(slot_destroy()));//登录界面关闭
    connect(m_pLoginDlg,SIGNAL(SIG_registerCommit(QString,QString,QString)),this,SLOT(slot_registerCommit(QString,QString,QString)));//登录提交
    m_pLoginDlg->show();

    m_pRoomdialog = new RoomDialog;

    connect(m_pRoomdialog,SIGNAL(SIg_close()),this,SLOT(slot_quitRoom()));//--绑定退出房间槽函数

    //添加网络
    m_pClient = new TcpClientMediator;

    //开启网络 需要给出连接欸的服务器的IP地址和端口号---有风险------
    m_pClient->OpenNet(m_serverIP.toStdString().c_str());
    //绑定信号和槽函数
    connect(m_pClient, SIGNAL(SIG_ReadyData(uint,char*,int)),this,SLOT(slot_dealData(uint,char*,int)));
}



//回收
void Ckernel::slot_destroy()
{
    qDebug()<<__func__;
    if(m_pWeChatDlg){
        m_pWeChatDlg->hide();//隐藏当前窗口
        delete m_pWeChatDlg;
        m_pWeChatDlg = NULL;
    }
    if(m_pLoginDlg){
        m_pLoginDlg->hide();//隐藏当前窗口
        delete m_pLoginDlg;
        m_pLoginDlg = NULL;
    }
    if(m_pClient){
        m_pClient->CloseNet();
        delete m_pClient;
        m_pClient = NULL;
    }

    exit(0);
}

//获取md5方法
#define MD5_KEY (1234)
static std::string GetMD5(QString value)
{
    QString str = QString("%1_%2").arg(value).arg(MD5_KEY);
    std::string strSrc = str.toStdString();
    MD5 md5( strSrc);
    return md5.toString();//返回使用md5加密完成之后的字符串
}

//提交登录信息
void Ckernel::slot_loginCommit(QString tel, QString pass)
{
    qDebug()<<__func__;
    std::string strTel  = tel.toStdString();
    //std::string strPass = pass.toStdString();


    STRU_LOGIN_RQ rq;
    strcpy_s(rq.tel, sizeof(rq.tel), strTel.c_str());

    std::string strPassMD5 = GetMD5(pass);
    qDebug()<<strPassMD5.c_str();//验证是否与网上的MD5相同
    strcpy_s(rq.password, sizeof(rq.password), strPassMD5.c_str());
    m_pClient->SendData(0,(char*)&rq,sizeof(rq));

}
//提交注册信息
void Ckernel::slot_registerCommit(QString name, QString tel, QString pass)
{
    qDebug()<<__func__;
    std::string strName = name.toStdString();
    std::string strTel  = tel.toStdString();
    //std::string strPass = pass.toStdString();//格式 UTF8  向数据库中插入数据，要兼容


    //兼容中文 utf8 QString -> std::string ---> char*
    STRU_REGISTER_RQ rq;//定义注册请求结构体
    strcpy_s(rq.name, sizeof(rq.name), strName.c_str());
    strcpy_s(rq.tel, sizeof(rq.tel), strTel.c_str());
    std::string strPassMD5 = GetMD5(pass);    //密码采用md5加密
    strcpy_s(rq.password, sizeof(rq.password), strPassMD5.c_str());


    m_pClient->SendData(0,(char*)&rq,sizeof(rq));

}
//网络处理
void Ckernel::slot_dealData(uint sock, char *buf, int nlen)
{
    int type = *(int*)buf; //前四个字节为协议头 根据不同的协议头跳转不同函数
    if(type >= _DEF_PACK_BASE && type <= _DEF_PACK_BASE+_DEF_PACK_COUNT)
    {
        //取得协议头，根据协议映射关系，找到函数指针
        PFUN pf = NetPackMap(type);
        if(pf){
           (this->*pf) (sock,buf,nlen);
        }
    }
    delete[] buf;
}
//登录回复处理
void Ckernel::slot_dealLoginRs(uint sock, char *buf, int nlen)
{
    qDebug()<<__func__;
    //拆包
    STRU_LOGIN_RS* rs =(STRU_LOGIN_RS*)buf;
    //根据返回结果 得到不同的信息
    switch(rs->result)
    {
        //电话已存在
        case user_not_exist:
        QMessageBox::about(m_pLoginDlg,"提示","用户不存在，登录失败");
        break;
        case password_error:
        QMessageBox::about(m_pLoginDlg,"提示","登录失败,密码错误");
        break;
        case login_success:
        {
            // QString strName = QString("用户[%1]登录成功").arg(rs->name);
            // QMessageBox::about(m_pLoginDlg,"提示",strName);
            m_name = QString::fromStdString(rs->name);
            qDebug()<<"登录测试-----------"<<m_name;
            m_pWeChatDlg->setInfo(m_name);
            m_id = rs->userid;//记录用户id
            //ui 跳转
            m_pLoginDlg->hide();
            m_pWeChatDlg->showNormal();
        }
        break;

    }


}

//注册回复处理
void Ckernel::slot_dealRegisterRs(uint sock, char *buf, int nlen)
{

    printf("客户端处理注册回复");
    //1.拆包
    STRU_REGISTER_RS* rs=(STRU_REGISTER_RS*)buf;
    //根据不同的结果，弹出不同的提示窗
    switch(rs->result)
    {
        //电话已存在
        case tel_is_exist:
        QMessageBox::about(m_pLoginDlg,"提示","手机号已存在，注册失败");
            break;
        //用户名已存在
        case name_is_exist:
            QMessageBox::about(m_pLoginDlg,"提示","用户名已存在，注册失败");
            break;
        //注册成功
        case register_success:
            QMessageBox::about(m_pLoginDlg,"提示","注册成功");
            break;

        default:
            break;
    }
}
//创建房间
void Ckernel::slot_createRoom()
{
    //判断是否在房间内 m_roomid
    if(m_roomid!=0)
    {
        QMessageBox::about(m_pWeChatDlg, "提示", "在房间内，无法创建，先退出");
        return;
    }
    //发命令 创建房间
    STRU_CREATEROOM_RQ rq;
    rq.m_UserID=m_id;
    m_pClient->SendData(0,(char*)&rq,sizeof(rq));
}

//加入房间
void Ckernel::slot_joinRoom()
{
    //判断是否在房间内 m_roomid
    if(m_roomid!=0)
    {
        QMessageBox::about(m_pWeChatDlg, "提示", "在房间内，无法加入，先退出");
        return;
    }
    QString strRoom = QInputDialog::getText(m_pWeChatDlg, "加入房间", "输入房间号");
    // 合理化判断
    QRegularExpression exp("^[0-9]{1,8}$");

    if (!exp.match(strRoom).hasMatch()) { // 正则表达式不匹配
        QMessageBox::about(m_pWeChatDlg, "提示", "房间号输入不合法,长度不超过8的数字");
        return;
    }
    qDebug()<<strRoom;
    //发命令 加入房间
    STRU_JOINROOM_RQ rq;
    rq.m_UserID=m_id;
    rq.m_RoomID=strRoom.toInt();

    m_pClient->SendData(0,(char*)&rq,sizeof(rq));
}
//退出房间
void Ckernel::slot_quitRoom()
{
    qDebug()<<__func__;//退出房间走了

    //发退出包
    STRU_LEAVEROOM_RQ rq;
    rq.m_nUserId = m_id;
    rq.m_RoomId = m_roomid;
    strcpy_s(rq.szUserName, sizeof(rq.szUserName), m_name.toStdString().c_str());
    m_pClient->SendData(0,(char*)&rq,sizeof(rq));
    qDebug()<<"测试发送的请求包是否有效 userid is: "<<rq.m_nUserId<<"  "<<"roomid is: "<<rq.m_RoomId<<" rq.szUserName ----is: "<<rq.szUserName;
    //关闭音频 视频 TODO

    //回收资源
    m_pRoomdialog->slot_clearUserShow();
    m_roomid = 0;
}

//创建房间处理
void Ckernel::slot_dealCreateRoomRs(uint sock, char *buf, int nlen)
{
    qDebug()<<__func__;
    //1.拆包
    STRU_CREATEROOM_RS* rs = (STRU_CREATEROOM_RS*)buf;

    //创建房间回复处理测试
    qDebug()<<rs->m_RoomId;
    //房间号 显示到界面 跳转
    m_pRoomdialog->slot_setInfo( QString::number(rs->m_RoomId));
    //服务器没有把个人信息发给你，你作为第一个进入房间的

    //把自己的信息放到房间里 做显示 todo
    UserShow* user = new UserShow;
    user->slot_setInfo(m_id,m_name);//设置自己的信息
    m_pRoomdialog->slot_addUserShow(user);//设置自己的信息到界面上

    m_roomid= rs->m_RoomId;
    m_pRoomdialog->showNormal();



}
//加入房间回复处理
void Ckernel::slot_dealJoinCRoomRs(uint sock, char *buf, int nlen)
{
    qDebug()<<__func__;
    //拆包
    STRU_JOINROOM_RS* rs = (STRU_JOINROOM_RS*)buf;

    //根据结果 失败 提示
    if(rs->m_lResult==0){
        QMessageBox::about(m_pWeChatDlg, "提示", "房间id不存在，加入失败");
        return;
    }
    //成功
    //房间号 显示到界面 跳转
    m_pRoomdialog->slot_setInfo( QString::number(rs->m_RoomID));
    if(rs->m_RoomID == 0){
         printf("传参错误\n");
    }
    qDebug()<<"-------roomid: "<<rs->m_RoomID;
    //服务器没有把个人信息发给你，你作为第一个进入房间的
    //跳转 roomid设置
    m_roomid= rs->m_RoomID;
    m_pRoomdialog->showNormal();


}

//房间成员请求处理
void Ckernel::slot_dealRoomMemberRq(uint sock, char *buf, int nlen)
{
    //拆包
    STRU_ROOM_MEMBER_RQ* rq = (STRU_ROOM_MEMBER_RQ*)buf;
    // rq->m_UserID;
    // rq->m_szUser;
    //创建用户对应的控件
    UserShow* user = new UserShow;
    user->slot_setInfo(rq->m_UserID,QString::fromStdString(rq->m_szUser));//设置自己的信息
    m_pRoomdialog->slot_addUserShow(user);//设置自己的信息到界面上

}
//离开房间的请求处理
void Ckernel::slot_dealLeaveRoomRq(uint sock, char *buf, int nlen)
{
    qDebug()<<__func__;
    //拆包
    STRU_LEAVEROOM_RQ* rq = (STRU_LEAVEROOM_RQ*)buf;
    //判断当前房间是否与自己的房间一样  把这个人 从ui上面去掉
    if(rq->m_RoomId == m_roomid)
    {
        m_pRoomdialog->slot_removeUserShow(rq->m_nUserId);
    }


}
