#############################################################################
# Makefile for building: WeChat
# Generated by qmake (3.1) (Qt 6.9.0)
# Project:  ..\..\WeChat.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../../WeChat -I. -I../../netapi/net -I../../netapi/mediator -I../../netapi -I../../MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -Idebug -I. -I/include -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lwsock32 -lws2_32 -lpthread -lMswsock D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Widgets.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Gui.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Network.a D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6Core.a -lmingw32 D:\PERSONAL\QT\6.9.0\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\PERSONAL\QT\6.9.0\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\netapi\mediator\INetMediator.cpp \
		..\..\netapi\mediator\TcpClientMediator.cpp \
		..\..\netapi\mediator\TcpServerMediator.cpp \
		..\..\netapi\mediator\UdpMediator.cpp \
		..\..\netapi\net\INet.cpp \
		..\..\netapi\net\TcpClient.cpp \
		..\..\netapi\net\TcpServer.cpp \
		..\..\netapi\net\UdpNet.cpp \
		..\..\MD5\md5.cpp \
		..\..\ckernel.cpp \
		..\..\logindialog.cpp \
		..\..\main.cpp \
		..\..\roomdialog.cpp \
		..\..\usershow.cpp \
		..\..\wechatdialog.cpp debug\qrc_resource.cpp \
		debug\moc_INetMediator.cpp \
		debug\moc_ckernel.cpp \
		debug\moc_logindialog.cpp \
		debug\moc_roomdialog.cpp \
		debug\moc_usershow.cpp \
		debug\moc_wechatdialog.cpp
OBJECTS       = debug/INetMediator.o \
		debug/TcpClientMediator.o \
		debug/TcpServerMediator.o \
		debug/UdpMediator.o \
		debug/INet.o \
		debug/TcpClient.o \
		debug/TcpServer.o \
		debug/UdpNet.o \
		debug/md5.o \
		debug/ckernel.o \
		debug/logindialog.o \
		debug/main.o \
		debug/roomdialog.o \
		debug/usershow.o \
		debug/wechatdialog.o \
		debug/qrc_resource.o \
		debug/moc_INetMediator.o \
		debug/moc_ckernel.o \
		debug/moc_logindialog.o \
		debug/moc_roomdialog.o \
		debug/moc_usershow.o \
		debug/moc_wechatdialog.o

DIST          =  ..\..\netapi\mediator\INetMediator.h \
		..\..\netapi\mediator\TcpClientMediator.h \
		..\..\netapi\mediator\TcpServerMediator.h \
		..\..\netapi\mediator\UdpMediator.h \
		..\..\netapi\net\INet.h \
		..\..\netapi\net\TcpClient.h \
		..\..\netapi\net\TcpServer.h \
		..\..\netapi\net\UdpNet.h \
		..\..\netapi\net\packdef.h \
		..\..\MD5\md5.h \
		..\..\ckernel.h \
		..\..\logindialog.h \
		..\..\roomdialog.h \
		..\..\usershow.h \
		..\..\wechatdialog.h ..\..\netapi\mediator\INetMediator.cpp \
		..\..\netapi\mediator\TcpClientMediator.cpp \
		..\..\netapi\mediator\TcpServerMediator.cpp \
		..\..\netapi\mediator\UdpMediator.cpp \
		..\..\netapi\net\INet.cpp \
		..\..\netapi\net\TcpClient.cpp \
		..\..\netapi\net\TcpServer.cpp \
		..\..\netapi\net\UdpNet.cpp \
		..\..\MD5\md5.cpp \
		..\..\ckernel.cpp \
		..\..\logindialog.cpp \
		..\..\main.cpp \
		..\..\roomdialog.cpp \
		..\..\usershow.cpp \
		..\..\wechatdialog.cpp
QMAKE_TARGET  = WeChat
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = WeChat.exe
DESTDIR_TARGET = debug\WeChat.exe

####### Build rules

first: all
all: Makefile.Debug  debug/WeChat.exe

debug/WeChat.exe: D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Widgets.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Gui.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Network.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6Core.a D:/PERSONAL/QT/6.9.0/mingw_64/lib/libQt6EntryPoint.a ui_logindialog.h ui_roomdialog.h ui_usershow.h ui_wechatdialog.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @debug\object_script.WeChat.Debug $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\WeChat.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) WeChat.zip $(SOURCES) $(DIST) ..\..\..\..\WeChat.pro D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\spec_pre.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\device_config.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\sanitize.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\gcc-base.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\g++-base.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\windows-vulkan.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\g++-win32.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\common\windows-desktop.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\qconfig.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_core.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_help.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_network.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt_functions.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt_config.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\win32-g++\qmake.conf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\spec_post.prf .qmake.stash D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exclusive_builds.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\toolchain.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\default_pre.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\default_pre.prf ..\..\netapi\netapi.pri ..\..\MD5\md5.pri D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resolve_config.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\default_post.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\build_pass.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qml_debug.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\precompile_header.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\warn_on.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\permissions.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qt.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resources_functions.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\resources.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\moc.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\opengl.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\uic.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\qmake_use.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\file_copies.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\win32\windows.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\testcase_targets.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\exceptions.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\yacc.prf D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\lex.prf ..\..\WeChat.pro ..\..\resource.qrc D:\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Widgets.prl D:\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Gui.prl D:\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Network.prl D:\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6Core.prl D:\PERSONAL\QT\6.9.0\mingw_64\lib\Qt6EntryPoint.prl   ..\..\resource.qrc D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp ..\..\netapi\mediator\INetMediator.h ..\..\netapi\mediator\TcpClientMediator.h ..\..\netapi\mediator\TcpServerMediator.h ..\..\netapi\mediator\UdpMediator.h ..\..\netapi\net\INet.h ..\..\netapi\net\TcpClient.h ..\..\netapi\net\TcpServer.h ..\..\netapi\net\UdpNet.h ..\..\netapi\net\packdef.h ..\..\MD5\md5.h ..\..\ckernel.h ..\..\logindialog.h ..\..\roomdialog.h ..\..\usershow.h ..\..\wechatdialog.h  ..\..\netapi\mediator\INetMediator.cpp ..\..\netapi\mediator\TcpClientMediator.cpp ..\..\netapi\mediator\TcpServerMediator.cpp ..\..\netapi\mediator\UdpMediator.cpp ..\..\netapi\net\INet.cpp ..\..\netapi\net\TcpClient.cpp ..\..\netapi\net\TcpServer.cpp ..\..\netapi\net\UdpNet.cpp ..\..\MD5\md5.cpp ..\..\ckernel.cpp ..\..\logindialog.cpp ..\..\main.cpp ..\..\roomdialog.cpp ..\..\usershow.cpp ..\..\wechatdialog.cpp ..\..\logindialog.ui ..\..\roomdialog.ui ..\..\usershow.ui ..\..\wechatdialog.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\INetMediator.o debug\TcpClientMediator.o debug\TcpServerMediator.o debug\UdpMediator.o debug\INet.o debug\TcpClient.o debug\TcpServer.o debug\UdpNet.o debug\md5.o debug\ckernel.o debug\logindialog.o debug\main.o debug\roomdialog.o debug\usershow.o debug\wechatdialog.o debug\qrc_resource.o debug\moc_INetMediator.o debug\moc_ckernel.o debug\moc_logindialog.o debug\moc_roomdialog.o debug\moc_usershow.o debug\moc_wechatdialog.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_resource.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_resource.cpp
debug/qrc_resource.cpp: ../../resource.qrc \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/rcc.exe \
		../../tx/36.jpg \
		../../tx/4.png \
		../../tx/1.png \
		../../tx/13.png \
		../../tx/0.png \
		../../tx/12.png \
		../../images/ok.png \
		../../images/register.png \
		../../images/sett.png \
		../../images/hat.png \
		../../images/tuer.png \
		../../images/color.png \
		../../bq/009.png \
		../../bq/2.png \
		../../bq/010.png \
		../../bq/11.png \
		../../bq/3.png \
		../../bq/4.png \
		../../bq/7.png \
		../../bq/6.png \
		../../bq/006.png \
		../../bq/14.png \
		../../bq/000.png \
		../../bq/1.png \
		../../bq/001.png \
		../../bq/13.png \
		../../bq/008.png \
		../../bq/003.png \
		../../bq/8.png \
		../../bq/10.png \
		../../bq/012.png \
		../../bq/005.png \
		../../bq/014.png \
		../../bq/007.png \
		../../bq/5.png \
		../../bq/002.png \
		../../bq/011.png \
		../../bq/004.png \
		../../bq/0.png \
		../../bq/12.png \
		../../bq/9.png \
		../../bq/013.png
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\rcc.exe -name resource --no-zstd ..\..\resource.qrc -o debug\qrc_resource.cpp

compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: D:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h D:\PERSONAL\QT\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_INetMediator.cpp debug/moc_ckernel.cpp debug/moc_logindialog.cpp debug/moc_roomdialog.cpp debug/moc_usershow.cpp debug/moc_wechatdialog.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_INetMediator.cpp debug\moc_ckernel.cpp debug\moc_logindialog.cpp debug\moc_roomdialog.cpp debug\moc_usershow.cpp debug\moc_wechatdialog.cpp
debug/moc_INetMediator.cpp: ../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\netapi\mediator\INetMediator.h -o debug\moc_INetMediator.cpp

debug/moc_ckernel.cpp: ../../ckernel.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		../../netapi/mediator/TcpClientMediator.h \
		../../netapi/mediator/INetMediator.h \
		../../netapi/net/packdef.h \
		../../logindialog.h \
		../../roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qgridlayout.h \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\ckernel.h -o debug\moc_ckernel.cpp

debug/moc_logindialog.cpp: ../../logindialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\logindialog.h -o debug\moc_logindialog.cpp

debug/moc_roomdialog.cpp: ../../roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qgridlayout.h \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\roomdialog.h -o debug\moc_roomdialog.cpp

debug/moc_usershow.cpp: ../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\usershow.h -o debug\moc_usershow.cpp

debug/moc_wechatdialog.cpp: ../../wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		debug/moc_predefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/moc.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/PERSONAL/QT/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/2025/Project/videoconferencing/WeChat -ID:/2025/Project/videoconferencing/WeChat/netapi/net -ID:/2025/Project/videoconferencing/WeChat/netapi/mediator -ID:/2025/Project/videoconferencing/WeChat/netapi -ID:/2025/Project/videoconferencing/WeChat/MD5 -ID:/PERSONAL/QT/6.9.0/mingw_64/include -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtNetwork -ID:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore -I. -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/PERSONAL/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/PERSONAL/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\wechatdialog.h -o debug\moc_wechatdialog.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_logindialog.h ui_roomdialog.h ui_usershow.h ui_wechatdialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_logindialog.h ui_roomdialog.h ui_usershow.h ui_wechatdialog.h
ui_logindialog.h: ../../logindialog.ui \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/uic.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\uic.exe ..\..\logindialog.ui -o ui_logindialog.h

ui_roomdialog.h: ../../roomdialog.ui \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/uic.exe \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\uic.exe ..\..\roomdialog.ui -o ui_roomdialog.h

ui_usershow.h: ../../usershow.ui \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/uic.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\uic.exe ..\..\usershow.ui -o ui_usershow.h

ui_wechatdialog.h: ../../wechatdialog.ui \
		D:/PERSONAL/QT/6.9.0/mingw_64/bin/uic.exe
	D:\PERSONAL\QT\6.9.0\mingw_64\bin\uic.exe ..\..\wechatdialog.ui -o ui_wechatdialog.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/INetMediator.o: ../../netapi/mediator/INetMediator.cpp ../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\INetMediator.o ..\..\netapi\mediator\INetMediator.cpp

debug/TcpClientMediator.o: ../../netapi/mediator/TcpClientMediator.cpp ../../netapi/mediator/TcpClientMediator.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../netapi/net/TcpClient.h \
		../../netapi/net/INet.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TcpClientMediator.o ..\..\netapi\mediator\TcpClientMediator.cpp

debug/TcpServerMediator.o: ../../netapi/mediator/TcpServerMediator.cpp ../../netapi/mediator/TcpServerMediator.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../netapi/net/TcpServer.h \
		../../netapi/net/INet.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TcpServerMediator.o ..\..\netapi\mediator\TcpServerMediator.cpp

debug/UdpMediator.o: ../../netapi/mediator/UdpMediator.cpp ../../netapi/mediator/UdpMediator.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../netapi/net/UdpNet.h \
		../../netapi/net/INet.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\UdpMediator.o ..\..\netapi\mediator\UdpMediator.cpp

debug/INet.o: ../../netapi/net/INet.cpp ../../netapi/net/INet.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\INet.o ..\..\netapi\net\INet.cpp

debug/TcpClient.o: ../../netapi/net/TcpClient.cpp ../../netapi/net/TcpClient.h \
		../../netapi/net/INet.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TcpClient.o ..\..\netapi\net\TcpClient.cpp

debug/TcpServer.o: ../../netapi/net/TcpServer.cpp ../../netapi/net/TcpServer.h \
		../../netapi/net/INet.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TcpServer.o ..\..\netapi\net\TcpServer.cpp

debug/UdpNet.o: ../../netapi/net/UdpNet.cpp ../../netapi/net/UdpNet.h \
		../../netapi/net/INet.h \
		../../netapi/mediator/INetMediator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\UdpNet.o ..\..\netapi\net\UdpNet.cpp

debug/md5.o: ../../MD5/md5.cpp ../../MD5/md5.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\md5.o ..\..\MD5\md5.cpp

debug/ckernel.o: ../../ckernel.cpp ../../ckernel.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		../../netapi/mediator/TcpClientMediator.h \
		../../netapi/mediator/INetMediator.h \
		../../netapi/net/packdef.h \
		../../logindialog.h \
		../../roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qgridlayout.h \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qDebug \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSettings \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsettings.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QFileInfo \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfileinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfile.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfiledevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatetime.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcalendar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtimezone.h \
		../../MD5/md5.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QInputDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qinputdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlineedit.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qframe.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextdocument.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextoption.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRegularExpression \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qregularexpression.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\ckernel.o ..\..\ckernel.cpp

debug/logindialog.o: ../../logindialog.cpp ../../logindialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		ui_logindialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QVariant \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QLabel \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlabel.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qframe.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpicture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextdocument.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QLineEdit \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlineedit.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextoption.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QPushButton \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QTabWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRegularExpression \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qregularexpression.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\logindialog.o ..\..\logindialog.cpp

debug/main.o: ../../main.cpp ../../ckernel.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		../../wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		../../netapi/mediator/TcpClientMediator.h \
		../../netapi/mediator/INetMediator.h \
		../../netapi/net/packdef.h \
		../../logindialog.h \
		../../roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qgridlayout.h \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\main.cpp

debug/roomdialog.o: ../../roomdialog.cpp ../../roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayout.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qgridlayout.h \
		../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		ui_roomdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QVariant \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\roomdialog.o ..\..\roomdialog.cpp

debug/usershow.o: ../../usershow.cpp ../../usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPaintEvent \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QImage \
		ui_usershow.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QPainter \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpainter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextoption.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpen.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\usershow.o ..\..\usershow.cpp

debug/wechatdialog.o: ../../wechatdialog.cpp ../../wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QDialog \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconfig.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qassert.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypes.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtversion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlogging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qflags.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qforeach.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qminmax.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qoverload.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qswap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtresource.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qwidget.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcompare.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstring.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qchar.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpair.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20functional.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q17memory.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmath.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiterable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmargins.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q23utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20utility.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qaction.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qkeysequence.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qicon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsize.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpaintdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrect.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcolor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgb.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qrgba64.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qimage.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpixelformat.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtransform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpolygon.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qregion.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qspan.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qline.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvariant.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qdebug.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qset.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qhash.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpalette.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbrush.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfont.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qendian.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontmetrics.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontinfo.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qcursor.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qbitmap.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qevent.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qurl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qeventpoint.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvector2d.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qvectornd.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpointingdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputdevice.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QList \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QObject \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QRect \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSize \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QSizeF \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QTransform \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qscreen_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qmutex.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qthread.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qexception.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qpromise.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qlocale.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QCloseEvent \
		ui_wechatdialog.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/QVariant \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/QIcon \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QApplication \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qapplication.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QLabel \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qlabel.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qframe.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qpicture.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtGui/qtextdocument.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QPushButton \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QScrollArea \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qscrollarea.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QToolButton \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qtoolbutton.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QWidget \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/QMessageBox \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/PERSONAL/QT/6.9.0/mingw_64/include/QtCore/qDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\wechatdialog.o ..\..\wechatdialog.cpp

debug/qrc_resource.o: debug/qrc_resource.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_resource.o debug\qrc_resource.cpp

debug/moc_INetMediator.o: debug/moc_INetMediator.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_INetMediator.o debug\moc_INetMediator.cpp

debug/moc_ckernel.o: debug/moc_ckernel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_ckernel.o debug\moc_ckernel.cpp

debug/moc_logindialog.o: debug/moc_logindialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_logindialog.o debug\moc_logindialog.cpp

debug/moc_roomdialog.o: debug/moc_roomdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_roomdialog.o debug\moc_roomdialog.cpp

debug/moc_usershow.o: debug/moc_usershow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_usershow.o debug\moc_usershow.cpp

debug/moc_wechatdialog.o: debug/moc_wechatdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_wechatdialog.o debug\moc_wechatdialog.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

