#include "logindialog.h"
#include "ui_logindialog.h"
#include <QRegularExpression> //正则表达式头文件 6.9与5不同
#include <QMessageBox>
LoginDialog::LoginDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::LoginDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("注册&登录");
    ui->tw_page->setCurrentIndex(0);//第一时间显示注册
}

LoginDialog::~LoginDialog()
{
    delete ui;
}
//登录窗口关闭事件
void LoginDialog::closeEvent(QCloseEvent *event)
{
    event->ignore();
    Q_EMIT SIG_close();//发送关闭信号
}

//清空登录信息
void LoginDialog::on_pb_clear_clicked()
{
    ui->le_tel->setText("");
    ui->le_password->setText("");
}
//提交登录信息
void LoginDialog::on_pb_commit_clicked()
{
    QString strTel = ui->le_tel->text();
    QString strPassword=ui->le_password->text();
    //校验
    // 非空校验   清掉空格之后 不能是空字符串
    QString tmpTel = strTel,tmpPass = strPassword;//由于清掉空格之后，字符串发生改变，所以需要使用两个临时变量来操作
    if( tmpTel.remove(" ").isEmpty() || tmpPass.remove(" ").isEmpty()){
        QMessageBox::about(this, "提示" , "手机号和密码不能为空格");//显示提示信息
        return;
    }


    //1.校验手机号  正则表达式 8-11位手机号
    //向qt5写法，再qt6写法
    QRegularExpression str("^1[3-8][1-9]\\d{6,9}$"); // 使用 QRegularExpression
    QRegularExpressionMatch match = str.match(strTel); // 使用 match 方法
   if (!match.hasMatch()) { // 检查是否有匹配
        QMessageBox::about(nullptr, "提示", "手机号格式错误，8-11位手机号");
        return;
    }

    //2.校验密码 长度不能超过20
    if(strPassword.length()>20){
        QMessageBox::about(this, "提示" , "密码过长");//
        return;
    }

    Q_EMIT SIG_loginCommit(strTel,strPassword);
}

//清空注册信息
void LoginDialog::on_pb_clear_register_clicked()
{
    ui->le_name_register->setText("");
    ui->le_tel_register->setText("");
    ui->le_password_register->setText("");
    ui->le_confirm_register->setText("");
}

//注册提交
void LoginDialog::on_pb_commit_register_clicked()
{
    //获取输入的内容
    QString strName=ui->le_name_register->text();//获取昵称
    QString strTel = ui->le_tel_register->text();//获取手机号
    QString strPassword=ui->le_password_register->text();//获取密码
    QString strConfirm = ui->le_confirm_register->text();//确认密码

    //校验
    // 非空校验   清掉空格之后 不能是空字符串
    QString tmpName=strName, tmpTel = strTel,tmpPass = strPassword,tmpConfirm = strConfirm;
    //由于后面要比对两次输入的密码是否相同，所以这里确认密码就不进行非空校验了
    if( tmpName.remove(" ").isEmpty()||tmpTel.remove(" ").isEmpty() || tmpPass.remove(" ").isEmpty()){
        QMessageBox::about(this, "提示" , "输入内容均不能为空格");//显示提示信息
        return;
    }


    //1.校验手机号  正则表达式 8-11位手机号
    //向qt5写法，再qt6写法
    QRegularExpression str("^1[3-8][1-9]\\d{6,9}$"); // 使用 QRegularExpression
    QRegularExpressionMatch match = str.match(strTel); // 使用 match 方法
    if (!match.hasMatch()) { // 检查是否有匹配
        QMessageBox::about(nullptr, "提示", "手机号格式错误，8-11位手机号");
        return;
    }

    //2.校验密码 长度不能超过20
    if(strPassword.length()>20){
        QMessageBox::about(this, "提示" , "密码过长，长度不超过20");
        return;
    }

    //3.校验两次输入的密码是否一致
    if(strPassword != strConfirm){
        QMessageBox::about(this, "提示" , "两次输入的密码不一致");
        return;
    }
    //4.name长度不超过10
    if(strName.length()>10){
        QMessageBox::about(this, "提示" , "昵称过长，长度不超过10");
        return;
    }

    Q_EMIT SIG_registerCommit(strName,strTel,strPassword);
}



