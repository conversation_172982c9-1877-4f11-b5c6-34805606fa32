/********************************************************************************
** Form generated from reading UI file 'wechatdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WECHATDIALOG_H
#define UI_WECHATDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_WeChatDialog
{
public:
    QWidget *widget;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QToolButton *tb_join;
    QToolButton *tb_create;
    QToolButton *tb_tool3;
    QPushButton *pb_icon;
    QLabel *lb_name;
    QPushButton *pb_set;

    void setupUi(QDialog *WeChatDialog)
    {
        if (WeChatDialog->objectName().isEmpty())
            WeChatDialog->setObjectName("WeChatDialog");
        WeChatDialog->resize(540, 620);
        WeChatDialog->setStyleSheet(QString::fromUtf8(""));
        widget = new QWidget(WeChatDialog);
        widget->setObjectName("widget");
        widget->setEnabled(true);
        widget->setGeometry(QRect(10, 10, 520, 600));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(81, 81, 81);"));
        scrollArea = new QScrollArea(widget);
        scrollArea->setObjectName("scrollArea");
        scrollArea->setGeometry(QRect(0, 180, 520, 420));
        QFont font;
        font.setPointSize(10);
        scrollArea->setFont(font);
        scrollArea->setFrameShape(QFrame::Shape::Box);
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName("scrollAreaWidgetContents");
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 516, 416));
        scrollArea->setWidget(scrollAreaWidgetContents);
        tb_join = new QToolButton(widget);
        tb_join->setObjectName("tb_join");
        tb_join->setGeometry(QRect(80, 100, 80, 70));
        QFont font1;
        font1.setFamilies({QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221")});
        font1.setPointSize(10);
        tb_join->setFont(font1);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/images/color.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        tb_join->setIcon(icon);
        tb_join->setIconSize(QSize(50, 50));
        tb_join->setToolButtonStyle(Qt::ToolButtonStyle::ToolButtonTextUnderIcon);
        tb_join->setAutoRaise(true);
        tb_create = new QToolButton(widget);
        tb_create->setObjectName("tb_create");
        tb_create->setGeometry(QRect(180, 100, 80, 70));
        tb_create->setFont(font1);
        tb_create->setIcon(icon);
        tb_create->setIconSize(QSize(50, 50));
        tb_create->setToolButtonStyle(Qt::ToolButtonStyle::ToolButtonTextUnderIcon);
        tb_create->setAutoRaise(true);
        tb_tool3 = new QToolButton(widget);
        tb_tool3->setObjectName("tb_tool3");
        tb_tool3->setGeometry(QRect(280, 100, 80, 70));
        tb_tool3->setFont(font1);
        tb_tool3->setIcon(icon);
        tb_tool3->setIconSize(QSize(50, 50));
        tb_tool3->setToolButtonStyle(Qt::ToolButtonStyle::ToolButtonTextUnderIcon);
        tb_tool3->setAutoRaise(true);
        pb_icon = new QPushButton(widget);
        pb_icon->setObjectName("pb_icon");
        pb_icon->setGeometry(QRect(70, 20, 60, 60));
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/tx/13.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pb_icon->setIcon(icon1);
        pb_icon->setIconSize(QSize(60, 60));
        pb_icon->setFlat(true);
        lb_name = new QLabel(widget);
        lb_name->setObjectName("lb_name");
        lb_name->setGeometry(QRect(150, 30, 200, 40));
        QFont font2;
        font2.setFamilies({QString::fromUtf8("\345\276\256\350\275\257\351\233\205\351\273\221")});
        font2.setPointSize(12);
        font2.setBold(false);
        lb_name->setFont(font2);
        pb_set = new QPushButton(widget);
        pb_set->setObjectName("pb_set");
        pb_set->setGeometry(QRect(400, 40, 40, 40));
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/images/sett.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pb_set->setIcon(icon2);
        pb_set->setIconSize(QSize(30, 30));

        retranslateUi(WeChatDialog);

        QMetaObject::connectSlotsByName(WeChatDialog);
    } // setupUi

    void retranslateUi(QDialog *WeChatDialog)
    {
        WeChatDialog->setWindowTitle(QCoreApplication::translate("WeChatDialog", "WeChatDialog", nullptr));
        tb_join->setText(QCoreApplication::translate("WeChatDialog", "\345\212\240\345\205\245\344\274\232\350\256\256", nullptr));
        tb_create->setText(QCoreApplication::translate("WeChatDialog", "\345\210\233\345\273\272\344\274\232\350\256\256", nullptr));
        tb_tool3->setText(QCoreApplication::translate("WeChatDialog", "\351\242\204\347\225\231", nullptr));
        pb_icon->setText(QString());
        lb_name->setText(QCoreApplication::translate("WeChatDialog", "\346\265\213\350\257\225\347\224\250\346\210\267", nullptr));
        pb_set->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class WeChatDialog: public Ui_WeChatDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WECHATDIALOG_H
